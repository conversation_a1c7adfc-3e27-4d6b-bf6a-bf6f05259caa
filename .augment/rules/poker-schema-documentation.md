---
type: "always_apply"
---

# Poker.ts 文档

## 概述

`src/rooms/schema/bean/Poker.ts` 文件定义了游戏中扑克牌的数据结构和业务逻辑，是 UNO 类型卡牌游戏的核心数据模型。该文件包含了卡牌的颜色、类型、元数据类以及相关的游戏规则逻辑。

**重要更新：** `PokerMeta` 类现在继承自 Colyseus 的 `Schema` 类，并使用 `@type` 装饰器进行属性注解，支持客户端与服务器之间的实时数据同步。

## Colyseus Schema 集成

### 导入依赖

```typescript
import { Schema, type } from "@colyseus/schema";
```

### Schema 注解说明

- `@type("int32")` - 用于整数类型（枚举值、数字）
- `@type("string")` - 用于字符串类型
- `@type([PokerMeta])` - 用于 PokerMeta 数组
- `@type(PokerMeta)` - 用于单个 PokerMeta 对象

### 网络同步特性

- 所有带有 `@type` 注解的属性都会自动在客户端和服务器之间同步
- 当属性值发生变化时，只有变化的部分会通过网络传输（增量更新）
- 客户端可以监听属性变化事件

## 枚举定义

### PokerColor（扑克牌颜色）

定义了扑克牌的颜色类型：

```typescript
export enum PokerColor {
    Red = 0,      // 红色
    Green = 1,    // 绿色
    Blue = 2,     // 蓝色
    Yellow = 3,   // 黄色
    None = 4,     // 万能卡未选择颜色时的状态
    Back = 5      // 卡牌背面
}
```

### PokerType（扑克牌类型）

定义了扑克牌的功能类型：

```typescript
export enum PokerType {
    Number = 0,       // 数字卡 (0-9)
    Skip = 1,         // 跳过下一个玩家
    Reverse = 2,      // 反转游戏方向
    DrawTwo = 3,      // +2 卡（下一个玩家抽2张牌）
    Wild = 4,         // 万能卡（可以改变颜色）
    WildDrawFour = 5, // 万能+4卡
    Back = 6          // 卡牌背面
}
```

## 核心类：PokerMeta

`PokerMeta` 类继承自 Colyseus 的 `Schema` 类，使用 `@type` 装饰器进行属性注解，支持网络同步。

### 类属性

- `@type("int32") type: PokerType` - 卡牌类型，使用 int32 类型进行网络同步
- `@type("int32") color: PokerColor` - 卡牌颜色，使用 int32 类型进行网络同步
- `@type("int32") value: number` - 数字卡的数值（0-9），其他类型卡为-1，使用 int32 类型进行网络同步
- `@type("string") resourceName: string` - 根据卡牌属性生成的资源名称，用于前端显示，使用 string 类型进行网络同步

### 构造函数

```typescript
constructor(type: PokerType = PokerType.Back,
            color: PokerColor = PokerColor.Back,
            value: number = -1)
```

**注意：** 构造函数中调用了 `super()` 来初始化父类 `Schema`。

**参数说明：**
- `type` - 卡牌类型，默认为背面
- `color` - 卡牌颜色，默认为背面
- `value` - 卡牌数值，默认为-1

### 核心方法



#### canPlayOn(topCard: PokerMeta): boolean

判断当前卡牌是否可以打在指定卡牌上。

**游戏规则：**
- 万能卡可以在任何时候打出
- 普通卡牌必须满足以下条件之一：
  - 颜色相同
  - 数字卡且数值相同
  - 类型相同（非数字卡）

**参数：**
- `topCard` - 当前桌面顶部的卡牌

**返回值：**
- `boolean` - 是否可以打出

#### setWildColor(color: PokerColor): void

为万能卡设置颜色（在玩家选择颜色后调用）。

**参数：**
- `color` - 要设置的颜色

**使用场景：**
- 玩家打出万能卡或万能+4卡后选择颜色时调用

### 静态工厂方法

#### createBackCard(): PokerMeta

创建一张背面卡牌。

**返回值：**
- 背面卡牌实例

#### createWildCard(isDrawFour: boolean = false): PokerMeta

创建万能卡。

**参数：**
- `isDrawFour` - 是否为万能+4卡，默认为false

**返回值：**
- 万能卡实例（初始颜色为None）

## 使用场景

### 1. 创建卡牌

```typescript
// 创建红色数字5 - 现在是 Colyseus Schema 对象，支持网络同步
const redFive = new PokerMeta(PokerType.Number, PokerColor.Red, 5);

// 创建蓝色跳过卡 - 自动生成 resourceName 为 'b_10'
const blueSkip = new PokerMeta(PokerType.Skip, PokerColor.Blue);

// 创建万能卡 - resourceName 为 'w_1'
const wildCard = PokerMeta.createWildCard();

// 创建万能+4卡 - resourceName 为 'w_4'
const wildDrawFour = PokerMeta.createWildCard(true);
```

### 1.1. 在 Colyseus Schema 中使用

```typescript
import { ArraySchema, Schema, type } from "@colyseus/schema";
import { PokerMeta } from "./bean/Poker";

export class GameState extends Schema {
    // 声明 PokerMeta 数组，支持网络同步
    @type([PokerMeta]) cardPile = new ArraySchema<PokerMeta>();

    // 声明单个 PokerMeta 对象
    @type(PokerMeta) topCard: PokerMeta;
}
```

### 2. 判断卡牌是否可以打出

```typescript
const topCard = new PokerMeta(PokerType.Number, PokerColor.Red, 7);
const playerCard = new PokerMeta(PokerType.Number, PokerColor.Blue, 7);

if (playerCard.canPlayOn(topCard)) {
    // 可以打出这张卡
}
```

### 3. 万能卡选择颜色

```typescript
const wildCard = PokerMeta.createWildCard();
wildCard.setWildColor(PokerColor.Red); // 玩家选择红色
```

## 维护规则

**重要：每当对 `src/rooms/schema/bean/Poker.ts` 文件进行任何修改时，必须同步更新此文档！**

### 需要更新文档的情况：

1. **添加新的枚举值**
   - 在 `PokerColor` 或 `PokerType` 中添加新值时
   - 更新对应的枚举说明和资源命名规则

2. **修改类属性**
   - 添加、删除或修改 `PokerMeta` 类的属性时
   - 更新类属性说明部分

3. **修改或添加方法**
   - 修改现有方法的逻辑、参数或返回值时
   - 添加新的公共方法时
   - 更新对应的方法说明和使用示例

4. **修改游戏规则**
   - 修改 `canPlayOn` 方法的判断逻辑时
   - 修改资源命名规则时
   - 更新相关的规则说明

5. **修改构造函数**
   - 修改构造函数参数或默认值时
   - 更新构造函数说明和使用示例

### 文档更新流程：

1. 修改代码后，立即更新此文档
2. 确保所有代码示例与实际代码保持一致
3. 验证文档中的说明准确反映代码功能
4. 如有必要，更新使用场景和示例代码

**注意：文档与代码不一致会导致开发团队理解错误，影响开发效率和代码质量。**
