import {Room, Client} from "@colyseus/core";
import {StateView} from "@colyseus/schema";

import {Player, UnoRoomState, UserState, UserCards} from "./schema/BuzUnoRoomState";
import {BuzUnoRoomEvent} from "./ServerEvents";
import {PokerMeta, PokerColor, PokerType} from "./schema/bean/Poker";

export class BuzUnoRoom extends Room<UnoRoomState> {
    maxClients = 4;
    state = new UnoRoomState();

    onCreate(options: any) {
        this.onMessage(BuzUnoRoomEvent.EVENT_READY_CHANGE, (client, message) => {
            const curUsr = client.userData as Player;
            console.log(`EVENT_READY_CHANGE ${curUsr}`)

            // 游戏已经开始时拒绝准备状态改变
            if (this.state.currenScene === UnoRoomState.SCENE_GAME) {
                console.log(`Player ${curUsr.name} tried to change ready state during game`);
                return;
            }

            curUsr.stateWrapper.state = curUsr.stateWrapper.state === UserState.NOT_READY ? UserState.READY : UserState.NOT_READY;
            //检查是否所有用户都准备了,那么开始游戏
            if (this.state.userList.length >= 2 && this.state.userList.every((user) => user.stateWrapper.state === UserState.READY)) {
                this.state.currenScene = UnoRoomState.SCENE_GAME;
                this.startGame();
            }
        });
    }

    onJoin(client: Client, options: any) {
        console.log(client.sessionId, "joined!");
        const userSize = this.state.userList.length
        const player = new Player("Player" + Math.floor(Math.random()*100), `${client.sessionId}`, `https://picsum.photos/seed/${Math.random()}/200/300`, new UserState())
        this.state.userList.push(player);
        client.userData = player;
        let stateView = new StateView();
        stateView.add(player)
        client.view = stateView
    }

    onLeave(client: Client, consented: boolean) {
        console.log(client.sessionId, "left!");
        this.state.userList.splice(this.state.userList.indexOf(client.userData as Player), 1);

        // 检查游戏是否应该结束
        this.checkGameEnd();
    }

    onDispose() {
        console.log("room", this.roomId, "disposing...");
    }

    /**
     * 开始游戏 - 转换玩家状态为 PLAYING
     */
    private startGame(): void {
        console.log("Starting UNO game...");

        try {
            // 验证游戏开始条件
            if (!this.validateGameStartConditions()) {
                console.error("Game start conditions not met");
                return;
            }

            // 将所有 READY 状态的玩家转换为 PLAYING 状态
            this.state.userList.forEach((player) => {
                if (player.stateWrapper.state === UserState.READY) {
                    player.stateWrapper.state = UserState.PLAYING;
                }
            });

            // 初始化卡牌系统
            this.initializeCards();

            // 分发卡牌给玩家
            this.dealCardsToPlayers();

            // 设置起始牌
            this.setInitialDiscardCard();

            // 设置第一个玩家回合
            this.setFirstPlayer();

            console.log(`Game started successfully with ${this.getPlayingPlayersCount()} players`);
        } catch (error) {
            console.error("Error starting game:", error);
            this.endGame(); // 出错时结束游戏
        }
    }

    /**
     * 获取当前正在游戏的玩家数量
     */
    private getPlayingPlayersCount(): number {
        return this.state.userList.filter(player => player.stateWrapper.state === UserState.PLAYING).length;
    }

    /**
     * 检查游戏是否应该结束（玩家数量少于2人）
     */
    private checkGameEnd(): void {
        const playingPlayersCount = this.getPlayingPlayersCount();

        if (playingPlayersCount < 2 && this.state.currenScene === UnoRoomState.SCENE_GAME) {
            console.log(`Game ending: only ${playingPlayersCount} players remaining`);
            this.endGame();
        }
    }

    /**
     * 结束游戏 - 重置所有状态
     */
    private endGame(): void {
        console.log("Ending UNO game...");

        // 重置所有玩家状态为 NOT_READY
        this.state.userList.forEach((player) => {
            player.stateWrapper.state = UserState.NOT_READY;
        });

        // 清空卡牌相关数据
        this.clearGameData();

        // 重置游戏场景
        this.state.currenScene = UnoRoomState.SCENE_WAITING;
        this.state.currentTurn = undefined;

        console.log("Game ended and reset to waiting state");
    }

    /**
     * 清空游戏数据
     */
    private clearGameData(): void {
        if (this.state.buzUnoCards) {
            // 清空卡牌堆
            this.state.buzUnoCards.cardPile.clear();
            // 清空弃牌区
            this.state.buzUnoCards.discardPile.clear();
            // 清空用户卡牌
            this.state.buzUnoCards.usersCardList.clear();
        }
    }

    /**
     * 初始化卡牌系统 - 创建完整的108张UNO卡牌
     */
    private initializeCards(): void {
        console.log("Initializing UNO card deck...");

        // 清空现有卡牌
        this.clearGameData();

        const cards: PokerMeta[] = [];

        // 创建4种颜色的卡牌 (每种颜色25张)
        const colors = [PokerColor.Red, PokerColor.Green, PokerColor.Blue, PokerColor.Yellow];

        for (const color of colors) {
            // 数字卡: 1张0号卡，2张1-9号卡 (共19张)
            cards.push(new PokerMeta(PokerType.Number, color, 0)); // 1张0号卡

            for (let value = 1; value <= 9; value++) {
                cards.push(new PokerMeta(PokerType.Number, color, value)); // 第一张
                cards.push(new PokerMeta(PokerType.Number, color, value)); // 第二张
            }

            // 功能卡: 每种2张 (共6张)
            cards.push(new PokerMeta(PokerType.Skip, color));
            cards.push(new PokerMeta(PokerType.Skip, color));

            cards.push(new PokerMeta(PokerType.Reverse, color));
            cards.push(new PokerMeta(PokerType.Reverse, color));

            cards.push(new PokerMeta(PokerType.DrawTwo, color));
            cards.push(new PokerMeta(PokerType.DrawTwo, color));
        }

        // 特殊卡: 4张万能卡 + 4张万能+4卡 (共8张)
        for (let i = 0; i < 4; i++) {
            cards.push(PokerMeta.createWildCard(false)); // 万能卡
            cards.push(PokerMeta.createWildCard(true));  // 万能+4卡
        }

        console.log(`Created ${cards.length} cards (should be 108)`);

        // 洗牌
        this.shuffleCards(cards);

        // 将卡牌添加到卡牌堆
        for (const card of cards) {
            this.state.buzUnoCards.cardPile.push(card);
        }

        console.log("Card deck initialized and shuffled");
    }

    /**
     * 洗牌算法 (Fisher-Yates shuffle)
     */
    private shuffleCards(cards: PokerMeta[]): void {
        for (let i = cards.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [cards[i], cards[j]] = [cards[j], cards[i]];
        }
    }

    /**
     * 为所有PLAYING状态的玩家分发7张牌
     */
    private dealCardsToPlayers(): void {
        console.log("Dealing cards to players...");

        const playingPlayers = this.state.userList.filter(player =>
            player.stateWrapper.state === UserState.PLAYING
        );

        if (playingPlayers.length === 0) {
            console.error("No playing players found for dealing cards");
            return;
        }

        // 检查卡牌数量是否足够
        const requiredCards = playingPlayers.length * 7;
        if (this.state.buzUnoCards.cardPile.length < requiredCards) {
            console.error(`Not enough cards to deal: need ${requiredCards}, have ${this.state.buzUnoCards.cardPile.length}`);
            return;
        }

        // 为每个玩家创建手牌
        for (const player of playingPlayers) {
            const userCards = new UserCards();
            userCards.uid = player.uid;

            // 分发7张牌
            for (let i = 0; i < 7; i++) {
                if (this.state.buzUnoCards.cardPile.length > 0) {
                    const card = this.state.buzUnoCards.cardPile.pop();
                    if (card) {
                        userCards.cards.push(card);
                    } else {
                        console.error(`Failed to deal card ${i + 1} to player ${player.name}`);
                    }
                } else {
                    console.error(`Card pile empty while dealing to player ${player.name}`);
                    break;
                }
            }

            this.state.buzUnoCards.usersCardList.push(userCards);
            console.log(`Dealt ${userCards.cards.length} cards to player ${player.name} (${player.uid})`);
        }
    }

    /**
     * 设置起始弃牌 - 从卡牌堆抽取1张有效起始牌
     */
    private setInitialDiscardCard(): void {
        console.log("Setting initial discard card...");

        let attempts = 0;
        const maxAttempts = 20; // 防止无限循环

        while (attempts < maxAttempts && this.state.buzUnoCards.cardPile.length > 0) {
            const card = this.state.buzUnoCards.cardPile.pop();

            if (card && this.isValidStartCard(card)) {
                this.state.buzUnoCards.discardPile.push(card);
                console.log(`Initial discard card set: ${card.resourceName}`);
                return;
            } else if (card) {
                // 如果是万能卡，放回卡牌堆底部
                this.state.buzUnoCards.cardPile.unshift(card);
            }

            attempts++;
        }

        // 如果找不到有效起始牌，创建一个默认的红色0号卡
        console.warn("Could not find valid start card, using default red 0");
        const defaultCard = new PokerMeta(PokerType.Number, PokerColor.Red, 0);
        this.state.buzUnoCards.discardPile.push(defaultCard);
    }

    /**
     * 检查卡牌是否可以作为起始牌 (不能是万能卡或万能+4卡)
     */
    private isValidStartCard(card: PokerMeta): boolean {
        return card.type !== PokerType.Wild && card.type !== PokerType.WildDrawFour;
    }

    /**
     * 验证游戏开始条件
     */
    private validateGameStartConditions(): boolean {
        const readyPlayers = this.state.userList.filter(player =>
            player.stateWrapper.state === UserState.READY
        );

        if (readyPlayers.length < 2) {
            console.error(`Not enough ready players: ${readyPlayers.length} (minimum 2)`);
            return false;
        }

        if (readyPlayers.length > 4) {
            console.error(`Too many ready players: ${readyPlayers.length} (maximum 4)`);
            return false;
        }

        if (!this.state.buzUnoCards) {
            console.error("BuzUnoCards not initialized");
            return false;
        }

        return true;
    }

    /**
     * 设置第一个玩家回合
     */
    private setFirstPlayer(): void {
        const playingPlayers = this.state.userList.filter(player =>
            player.stateWrapper.state === UserState.PLAYING
        );

        if (playingPlayers.length > 0) {
            // 随机选择第一个玩家
            const randomIndex = Math.floor(Math.random() * playingPlayers.length);
            this.state.currentTurn = playingPlayers[randomIndex].uid;
            console.log(`First player set: ${playingPlayers[randomIndex].name} (${this.state.currentTurn})`);
        } else {
            console.error("No playing players found to set as first player");
        }
    }

    /**
     * 获取玩家的手牌数量
     */
    private getPlayerCardCount(playerUid: string): number {
        const userCards = this.state.buzUnoCards.usersCardList.find(uc => uc.uid === playerUid);
        return userCards ? userCards.cards.length : 0;
    }

    /**
     * 检查游戏状态的完整性
     */
    private validateGameState(): boolean {
        try {
            // 检查卡牌总数
            const totalCards = this.state.buzUnoCards.cardPile.length +
                              this.state.buzUnoCards.discardPile.length +
                              this.state.buzUnoCards.usersCardList.reduce((sum, uc) => sum + uc.cards.length, 0);

            if (totalCards !== 108) {
                console.warn(`Total cards mismatch: ${totalCards} (expected 108)`);
            }

            // 检查玩家状态一致性
            const playingPlayers = this.getPlayingPlayersCount();
            const userCardsCount = this.state.buzUnoCards.usersCardList.length;

            if (playingPlayers !== userCardsCount) {
                console.warn(`Player count mismatch: ${playingPlayers} playing, ${userCardsCount} with cards`);
            }

            return true;
        } catch (error) {
            console.error("Game state validation error:", error);
            return false;
        }
    }

    /**
     * 安全地从数组中移除元素
     */
    private safeRemoveFromArray<T>(array: T[], item: T): boolean {
        const index = array.indexOf(item);
        if (index > -1) {
            array.splice(index, 1);
            return true;
        }
        return false;
    }

}

