import {ArraySchema, Schema, type, view} from "@colyseus/schema";

/**
 * 游戏阶段枚举
 */
enum GamePhase {
    // 等待玩家 所有玩家准备好后且人数大于等于 2 进入PLAYING阶段
    WAITING = 0,
    // 游戏进行中
    PLAYING = 1,
    //游戏结束展示VictoryInfo信息 5s 后重新进入WAITING
    GAME_END = 5        // 游戏结束
}

/**
 * 玩家游戏状态枚举
 */
enum PlayerGameState {
    NOT_READY = 0,      // 未准备
    READY = 1,          // 已准备
    PLAYING  = 3,      // 游戏中
    WATCHING = 4        // 观战模式
}

/**
 * 游戏方向枚举
 */
enum GameDirection {
    CLOCK_WISE = 0,      // 顺时针
    COUNTER_CLOCK_WISE = 1 // 逆时针
}

enum PokerColor {
    Red = 0,
    Green = 1,
    Blue = 2,
    Yellow = 3,
    None = 4, // For wild cards before color selection
    Back = 5  // Card back side
}

enum PokerType {
    Number = 0,       // Number cards (0-9)
    Skip = 1,         // Skip next player
    Reverse = 2,      // Reverse direction
    DrawTwo = 3,      // +2 card
    Wild = 4,         // Wild card (change color)
    WildDrawFour = 5, // Wild +4 card
    Back = 6          // Card back
}

class PokerMeta  {
    public type: PokerType;
    public color: PokerColor;
    public value: number; // For number cards (0-9)

    constructor(type: PokerType = PokerType.Back,
                color: PokerColor = PokerColor.Back,
                value: number = -1) {
        this.type = type;
        this.color = color;
        this.value = value;
    }
}

//用户信息
class Player{
    public id: string;
    public name: string;
    public avatar: string;
    //是否喊了UNO. 只有剩下两张牌且可以出的情况下才可以喊.
    //如果不是你的回合被由于其他卡牌效果影响手牌大于 1 那么重置为false
    public unoDeclared:boolean;
    //玩家状态
    public playerGameState:PlayerGameState
    //手牌
    public cards: PokerMeta[] = [];
    //观众模式的时候才有意义,指向观光人的
    public buzWatchID?:string

    constructor(id: string, name: string, avatar: string,playerGameState:PlayerGameState,buzWatchID?:string) {
        this.id = id;
        this.name = name;
        this.avatar = avatar;
        this.unoDeclared = false;
        this.playerGameState = playerGameState;
        this.buzWatchID = buzWatchID
    }
}




class RoomStatus {
    //游戏玩家
    public players:Player[] = [];
   
    //当前轮到谁出牌
    public playerTurn:string = ""
    //抽牌区域
    public drawPile:PokerMeta[] = []
    //出牌区
    public discardPile:PokerMeta[] = []
    //游戏方向
    public gameDirection:GameDirection = GameDirection.CLOCK_WISE
    //
    public gamePhase:GamePhase = GamePhase.WAITING
}
